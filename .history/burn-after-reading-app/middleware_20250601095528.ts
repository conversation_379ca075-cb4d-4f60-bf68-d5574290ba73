// middleware.ts
import { createI18nMiddleware } from 'next-international/middleware';
import { NextRequest } from 'next/server';

const I18nMiddleware = createI18nMiddleware({
  locales: ['en-US', 'zh-CN'],
  defaultLocale: 'zh-CN',
  // urlMappingStrategy: 'rewrite', // 可选配置，默认为 'redirect'
});

export function middleware(request: NextRequest) {
  return I18nMiddleware(request);
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};
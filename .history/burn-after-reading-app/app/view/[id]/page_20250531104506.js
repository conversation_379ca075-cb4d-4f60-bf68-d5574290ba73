// burn-after-reading-app/app/view/[id]/page.js
'use client'; // 标记为客户端组件，因为我们可能会在这里处理表单交互

import Layout from '../../../../components/Layout'; // 引入布局组件
import { useState, useEffect } from 'react'; // 引入 React Hooks useState 和 useEffect
import { notFound } from 'next/navigation'; // 引入 notFound 用于显示404页面

/**
 * @file 消息展示页面 (动态路由)
 * @module ViewMessagePage
 * @param {object} props - 组件属性
 * @param {object} props.params - 路由参数
 * @param {string} props.params.id - 消息的ID (从URL中获取)
 */

/**
 * 消息展示页面组件。
 * 用于处理密码输入（如果消息被加密）和展示消息内容。
 * @param {object} props - 组件属性。
 * @param {object} props.params - 路由参数。
 * @param {string} props.params.id - 从URL中提取的消息ID。
 * @returns {JSX.Element} 消息展示页面的JSX元素。
 */
export default function ViewMessagePage({ params }) {
  /**
   * @state
   * @property {string} inputPassword - 用户输入的密码。
   */
  const [inputPassword, setInputPassword] = useState('');

  /**
   * @state
   * @property {boolean} showPasswordForm - 控制密码输入表单的显示与隐藏，默认为 true (显示)。
   */
  const [showPasswordForm, setShowPasswordForm] = useState(true);

  /**
   * @state
   * @property {boolean} showMessage - 控制消息内容的显示与隐藏，默认为 false (隐藏)。
   */
  const [showMessage, setShowMessage] = useState(false);
  // const messageId = params.id; // 暂时未使用，但保留以备后续逻辑。在实际应用中，此ID将用于获取消息数据。
/**
   * @state
   * @property {object | null} messageData - 从API获取的消息数据，初始为 null。
   */
  const [messageData, setMessageData] = useState(null);

  /**
   * @state
   * @property {boolean} isLoading - 指示数据是否正在加载，初始为 true。
   */
  const [isLoading, setIsLoading] = useState(true);

  /**
   * @state
   * @property {object | null} error - 存储API请求相关的错误信息，初始为 null。
   */
  const [error, setError] = useState(null);

  /**
   * 处理密码输入框内容变化的事件回调函数。
   * @param {React.ChangeEvent<HTMLInputElement>} event - 事件对象。
   */
  const handlePasswordInputChange = (event) => {
    setInputPassword(event.target.value);
  };

  /**
   * 处理“查看”按钮点击的事件回调函数。
   * 模拟密码验证逻辑：打印输入的密码到控制台，然后隐藏密码表单并显示消息内容。
   * @param {React.MouseEvent<HTMLButtonElement>} event - 事件对象 (或者 React.FormEvent<HTMLFormElement> 如果绑定在form的onSubmit上)。
   */
  const handleViewMessageClick = (event) => {
    // 如果事件是从表单提交触发的，需要阻止默认行为
    if (event && typeof event.preventDefault === 'function') {
      event.preventDefault();
    }
    console.log("尝试的密码:", inputPassword);
    // 模拟密码“正确”的逻辑
    setShowPasswordForm(false);
    setShowMessage(true);
  };

  return (
    <Layout>
      <div className="max-w-xl mx-auto p-4 sm:p-6 lg:p-8 bg-white shadow-lg rounded-lg">
        {/* 条件渲染：如果 showPasswordForm 为 true，则显示密码输入表单 */}
        {showPasswordForm && (
          <>
            <h2 className="text-xl font-semibold text-gray-800 mb-4">查看加密消息</h2>
            {/* 密码输入表单 */}
            <form onSubmit={handleViewMessageClick} className="space-y-4">
              <div>
                <label htmlFor="accessPassword" className="block text-sm font-medium text-gray-700 mb-1">
                  请输入访问密码：
                </label>
                <input
                  type="password"
                  id="accessPassword"
                  name="accessPassword"
                  value={inputPassword} // 绑定状态 inputPassword
                  onChange={handlePasswordInputChange} // 绑定密码输入变化事件
                  className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="输入密码"
                />
              </div>
              <div>
                {/* 查看按钮，点击时触发 handleViewMessageClick */}
                <button
                  type="submit" // 保持 submit 类型以利用表单的 onSubmit
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  查看
                </button>
              </div>
            </form>
          </>
        )}

        {/* 条件渲染：如果 showMessage 为 true，则显示消息内容 */}
        {showMessage && (
          <>
            <h2 className="text-xl font-semibold text-gray-800 mb-4">秘密消息内容</h2>
            <div className="p-4 bg-gray-50 border border-gray-200 rounded-md">
              <p className="text-gray-700">
                这是秘密消息内容... (此为占位文本，真实消息将在后续实现中获取和展示)
              </p>
            </div>
          </>
        )}
      </div>
    </Layout>
  );
}
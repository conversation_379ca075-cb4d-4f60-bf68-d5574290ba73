// burn-after-reading-app/app/view/[id]/page.js
'use client'; // 标记为客户端组件，因为我们可能会在这里处理表单交互

import Layout from '../../../../components/Layout'; // 引入布局组件
import { useState } from 'react'; // 引入 useState 用于处理密码输入和消息显示状态

/**
 * 消息展示页面 (动态路由)
 * @param {object} params - 路由参数
 * @param {string} params.id - 消息的ID (从URL中获取)
 */
export default function ViewMessagePage({ params }) {
  const [password, setPassword] = useState('');
  const [showPasswordPrompt, setShowPasswordPrompt] = useState(true); // 初始显示密码输入
  const [showMessage, setShowMessage] = useState(false);
  // const messageId = params.id; // 暂时未使用，但保留以备后续逻辑

  // 模拟密码验证逻辑
  const handlePasswordSubmit = (event) => {
    event.preventDefault();
    // 在真实应用中，这里会进行密码验证
    // 此处简化为：输入任何内容都视为密码正确
    if (password) {
      setShowPasswordPrompt(false);
      setShowMessage(true);
    } else {
      // 可以添加密码为空的提示
      alert('请输入密码！');
    }
  };

  return (
    <Layout>
      <div className="max-w-xl mx-auto p-4 sm:p-6 lg:p-8 bg-white shadow-lg rounded-lg">
        {showPasswordPrompt && (
          <>
            <h2 className="text-xl font-semibold text-gray-800 mb-4">查看加密消息</h2>
            <form onSubmit={handlePasswordSubmit} className="space-y-4">
              <div>
                <label htmlFor="accessPassword" className="block text-sm font-medium text-gray-700 mb-1">
                  请输入访问密码：
                </label>
                <input
                  type="password"
                  id="accessPassword"
                  name="accessPassword"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="输入密码"
                />
              </div>
              <div>
                <button
                  type="submit"
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  查看
                </button>
              </div>
            </form>
          </>
        )}

        {showMessage && (
          <>
            <h2 className="text-xl font-semibold text-gray-800 mb-4">秘密消息内容</h2>
            <div className="p-4 bg-gray-50 border border-gray-200 rounded-md">
              <p className="text-gray-700">
                这是秘密消息内容... (此为占位文本，真实消息将在后续实现中获取和展示)
              </p>
            </div>
          </>
        )}
      </div>
    </Layout>
  );
}
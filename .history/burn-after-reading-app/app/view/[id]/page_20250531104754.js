// burn-after-reading-app/app/view/[id]/page.js
'use client'; // 标记为客户端组件，因为我们可能会在这里处理表单交互

import Layout from '../../../../components/Layout'; // 引入布局组件
import { useState, useEffect } from 'react'; // 引入 React Hooks useState 和 useEffect
import { notFound } from 'next/navigation'; // 引入 notFound 用于显示404页面

/**
 * @file 消息展示页面 (动态路由)
 * @module ViewMessagePage
 * @param {object} props - 组件属性
 * @param {object} props.params - 路由参数
 * @param {string} props.params.id - 消息的ID (从URL中获取)
 */

/**
 * 消息展示页面组件。
 * 用于处理密码输入（如果消息被加密）和展示消息内容。
 * @param {object} props - 组件属性。
 * @param {object} props.params - 路由参数。
 * @param {string} props.params.id - 从URL中提取的消息ID。
 * @returns {JSX.Element} 消息展示页面的JSX元素。
 */
export default function ViewMessagePage({ params }) {
  /**
   * @state
   * @property {string} inputPassword - 用户输入的密码。
   */
  const [inputPassword, setInputPassword] = useState('');

  /**
   * @state
   * @property {boolean} showPasswordForm - 控制密码输入表单的显示与隐藏，默认为 true (显示)。
   */
  const [showPasswordForm, setShowPasswordForm] = useState(true);

  /**
   * @state
   * @property {boolean} showMessage - 控制消息内容的显示与隐藏，默认为 false (隐藏)。
   */
  const [showMessage, setShowMessage] = useState(false);
  // const messageId = params.id; // 暂时未使用，但保留以备后续逻辑。在实际应用中，此ID将用于获取消息数据。
/**
   * @state
   * @property {object | null} messageData - 从API获取的消息数据，初始为 null。
   */
  const [messageData, setMessageData] = useState(null);

  /**
   * @state
   * @property {boolean} isLoading - 指示数据是否正在加载，初始为 true。
   */
  const [isLoading, setIsLoading] = useState(true);

  /**
   * @state
   * @property {object | null} error - 存储API请求相关的错误信息，初始为 null。
   */
  const [error, setError] = useState(null);

/**
   * @effect
   * 当组件加载或 `params.id` 发生变化时，此 Hook 会执行。
   * 它负责从API获取消息数据。
   */
  useEffect(() => {
    const messageId = params.id; // 从路由参数中获取消息ID

    if (messageId) {
      /**
       * 异步函数，用于获取消息数据。
       */
      const fetchData = async () => {
        setIsLoading(true); // 开始加载，设置加载状态为 true
        setError(null); // 重置错误状态
        // setShowPasswordForm(true); // 初始时总是尝试显示密码表单或加载状态
        // setShowMessage(false); // 初始隐藏消息

        try {
          const response = await fetch(`/api/message/${messageId}`);
          if (response.ok) {
            const data = await response.json();
            if (data.success) {
              setMessageData(data); // 存储从API获取的完整数据
              if (data.requiresPassword) {
                // 如果消息需要密码，则保持密码输入框的显示逻辑
                // UI将根据 messageData.requiresPassword 和 showPasswordForm 来决定是否显示密码框
                setShowPasswordForm(true);
                setShowMessage(false); // 确保消息此时不直接显示
              } else {
                // 如果消息不需要密码，则直接显示消息
                setShowPasswordForm(false);
                setShowMessage(true);
              }
            } else {
              // API 返回 success: false，例如消息不存在或已销毁
              setError({ message: data.error || '无法加载消息，可能已被销毁或不存在。' });
              if (data.status === 404) {
                notFound(); // 如果是404，则调用notFound()显示Next.js的404页面
              }
            }
          } else {
            // 处理API请求失败的情况 (例如 500 Internal Server Error)
            let errorData;
            try {
              errorData = await response.json();
            } catch (e) {
              errorData = { message: response.statusText || '获取消息失败，服务器错误。' };
            }
            setError({ message: `错误 ${response.status}: ${errorData.message || '获取消息时发生未知错误。'}` });
          }
        } catch (err) {
          // 处理网络错误或其他在 fetch 过程中发生的异常
          console.error('获取消息时发生网络错误:', err);
          setError({ message: '网络错误，请检查您的连接并重试。' });
        } finally {
          setIsLoading(false); // 加载结束，设置加载状态为 false
        }
      };

      fetchData();
    } else {
      // 如果没有 messageId (例如路由参数丢失)，则直接显示错误或404
      setError({ message: '无效的消息ID。' });
      setIsLoading(false);
      notFound();
    }
  }, [params.id]); // 依赖数组中包含 params.id，当ID变化时重新获取数据
  /**
   * 处理密码输入框内容变化的事件回调函数。
   * @param {React.ChangeEvent<HTMLInputElement>} event - 事件对象。
   */
  const handlePasswordInputChange = (event) => {
    setInputPassword(event.target.value);
  };

  /**
   * 处理“查看”按钮点击的事件回调函数。
   * 模拟密码验证逻辑：打印输入的密码到控制台，然后隐藏密码表单并显示消息内容。
   * @param {React.MouseEvent<HTMLButtonElement>} event - 事件对象 (或者 React.FormEvent<HTMLFormElement> 如果绑定在form的onSubmit上)。
   */
  const handleViewMessageClick = (event) => {
    // 如果事件是从表单提交触发的，需要阻止默认行为
    if (event && typeof event.preventDefault === 'function') {
      event.preventDefault();
    }
    // 根据任务简化逻辑：如果 messageData 存在且 requiresPassword 为 true,
    // 用户点击“查看”后，我们即认为密码“正确”（不实际验证 inputPassword），
    // 并显示已通过 useEffect 获取的消息内容。
    if (messageData && messageData.requiresPassword) {
      console.log("用户尝试查看受密码保护的消息，密码输入为:", inputPassword); // 记录用户输入的密码，尽管我们不验证它
      setShowPasswordForm(false); // 隐藏密码表单
      setShowMessage(true);     // 显示消息内容
    } else if (messageData && !messageData.requiresPassword) {
      // 如果消息 изначально就不需要密码 (这种情况理论上 useEffect 已处理，但作为防御性措施)
      setShowPasswordForm(false);
      setShowMessage(true);
    }
    // 如果 messageData 为 null (例如 useEffect 还在加载或失败)，此函数不应改变显示状态，
    // UI 应由 isLoading 和 error 状态驱动。
  };

  return (
    <Layout>
      <div className="max-w-xl mx-auto p-4 sm:p-6 lg:p-8 bg-white shadow-lg rounded-lg">
        {/* 条件渲染：如果 showPasswordForm 为 true，则显示密码输入表单 */}
        {showPasswordForm && (
          <>
            <h2 className="text-xl font-semibold text-gray-800 mb-4">查看加密消息</h2>
            {/* 密码输入表单 */}
            <form onSubmit={handleViewMessageClick} className="space-y-4">
              <div>
                <label htmlFor="accessPassword" className="block text-sm font-medium text-gray-700 mb-1">
                  请输入访问密码：
                </label>
                <input
                  type="password"
                  id="accessPassword"
                  name="accessPassword"
                  value={inputPassword} // 绑定状态 inputPassword
                  onChange={handlePasswordInputChange} // 绑定密码输入变化事件
                  className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="输入密码"
                />
              </div>
              <div>
                {/* 查看按钮，点击时触发 handleViewMessageClick */}
                <button
                  type="submit" // 保持 submit 类型以利用表单的 onSubmit
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  查看
                </button>
              </div>
            </form>
          </>
        )}

        {/* 条件渲染：如果 showMessage 为 true，则显示消息内容 */}
        {showMessage && (
          <>
            <h2 className="text-xl font-semibold text-gray-800 mb-4">秘密消息内容</h2>
            <div className="p-4 bg-gray-50 border border-gray-200 rounded-md">
              <p className="text-gray-700">
                这是秘密消息内容... (此为占位文本，真实消息将在后续实现中获取和展示)
              </p>
            </div>
          </>
        )}
      </div>
    </Layout>
  );
}
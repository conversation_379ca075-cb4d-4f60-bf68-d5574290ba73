// burn-after-reading-app/app/[locale]/view/[id]/page.js
'use client';

import Layout from '../../../../../components/Layout'; // 调整Layout组件的导入路径
import { useState, useEffect } from 'react';
import { notFound, useParams } from 'next/navigation'; // useParams 用于获取 locale
import { useI18n } from '../../../../app/i18n'; // 调整 i18n hook 的导入路径

export default function ViewMessagePage({ params: routeParams }) {
  // routeParams 来自 Next.js 路由系统，包含 id
  // useParams hook 用于获取 locale，因为此组件在 [locale] 目录下
  const internalParams = useParams();
  const locale = internalParams.locale; // 获取当前 locale
  const messageId = routeParams.id; // 获取消息 ID

  const t = useI18n();
  const [inputPassword, setInputPassword] = useState('');
  const [showPasswordForm, setShowPasswordForm] = useState(true);
  const [showMessage, setShowMessage] = useState(false);
  const [messageData, setMessageData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isVerifyingPassword, setIsVerifyingPassword] = useState(false);
  const [passwordError, setPasswordError] = useState(null);

  useEffect(() => {
    if (messageId) {
      const fetchData = async () => {
        setIsLoading(true);
        setError(null);

        try {
          // API路由不需要 locale 前缀
          const response = await fetch(`/api/message/${messageId}`);
          if (response.ok) {
            const data = await response.json();
            if (data.success) {
              setMessageData(data);
              if (data.requiresPassword) {
                setShowPasswordForm(true);
                setShowMessage(false);
              } else {
                setShowPasswordForm(false);
                setShowMessage(true);
              }
            } else {
              setError({ message: data.error || t('view.error.not_found') });
              if (data.status === 404) {
                notFound();
              }
            }
          } else {
            let errorData;
            try {
              errorData = await response.json();
            } catch (e) {
              errorData = { message: response.statusText || t('view.error.generic') };
            }
            setError({ message: `${t('view.error.generic')} (${response.status}): ${errorData.message || ''}` });
          }
        } catch (err) {
          console.error('获取消息时发生网络错误:', err);
          setError({ message: t('view.error.generic') });
        } finally {
          setIsLoading(false);
        }
      };
      fetchData();
    } else {
      setError({ message: t('view.error.not_found') }); // 或者更具体的无效ID错误
      setIsLoading(false);
      notFound();
    }
  }, [messageId, t]); // 添加 t 到依赖项数组

  const handlePasswordInputChange = (event) => {
    setInputPassword(event.target.value);
  };

  const handleViewMessageClick = async (event) => {
    if (event && typeof event.preventDefault === 'function') {
      event.preventDefault();
    }

    if (!messageData || !messageData.requiresPassword) {
      return;
    }

    if (!inputPassword.trim()) {
      setPasswordError(t('view.error.password_required') || '请输入密码');
      return;
    }

    setIsVerifyingPassword(true);
    setPasswordError(null);

    try {
      const response = await fetch(`/api/message/${messageId}`, {
        method: 'GET',
        headers: {
          'x-message-password': inputPassword,
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.messageContent) {
          setMessageData(data);
          setShowPasswordForm(false);
          setShowMessage(true);
        } else {
          setPasswordError(data.error || t('view.error.password_incorrect') || '密码错误');
          setInputPassword('');
        }
      } else if (response.status === 401) {
        setPasswordError(t('view.error.password_incorrect') || '密码错误');
        setInputPassword('');
      } else {
        const errorData = await response.json().catch(() => ({}));
        setPasswordError(errorData.error || t('view.error.generic') || '验证失败，请重试');
      }
    } catch (error) {
      console.error('密码验证时发生错误:', error);
      setPasswordError(t('view.error.generic') || '网络错误，请重试');
    } finally {
      setIsVerifyingPassword(false);
    }
  };

  return (
    <Layout>
      <div className="max-w-xl mx-auto p-4 sm:p-6 lg:p-8 bg-white shadow-lg rounded-lg">
        {isLoading && (
          <div className="text-center py-4">
            <p className="text-lg font-medium text-gray-700">{t('view.loading') || '加载中，请稍候...'}</p>
          </div>
        )}

        {error && !isLoading && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <strong className="font-bold">{t('view.error.title') || '发生错误：'}</strong>
            <span className="block sm:inline">{error.message}</span>
          </div>
        )}

        {!isLoading && !error && messageData && (
          <>
            {showPasswordForm && messageData.requiresPassword && !showMessage && (
              <>
                <h2 className="text-xl font-semibold text-gray-800 mb-4">{t('view.title.encrypted') || '查看加密消息'}</h2>
                <p className="text-sm text-gray-600 mb-4">{t('view.prompt.password')}</p>

                {passwordError && (
                  <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4" role="alert">
                    <span className="block sm:inline">{passwordError}</span>
                  </div>
                )}

                <form onSubmit={handleViewMessageClick} className="space-y-4">
                  <div>
                    <label htmlFor="accessPassword" className="block text-sm font-medium text-gray-700 mb-1">
                      {t('view.label.password')}
                    </label>
                    <input
                      type="password"
                      id="accessPassword"
                      name="accessPassword"
                      value={inputPassword}
                      onChange={handlePasswordInputChange}
                      className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      placeholder={t('view.placeholder.password')}
                      required
                      disabled={isVerifyingPassword}
                    />
                  </div>
                  <div>
                    <button
                      type="submit"
                      disabled={isVerifyingPassword}
                      className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isVerifyingPassword ? (t('view.button.verifying') || '验证中...') : (t('view.button.decrypt') || '查看消息')}
                    </button>
                  </div>
                </form>
              </>
            )}

            {showMessage && messageData.messageContent && (
              <>
                <h2 className="text-xl font-semibold text-gray-800 mb-4">{t('view.title.content') || '秘密消息内容'}</h2>
                <div className="p-4 bg-gray-50 border border-gray-200 rounded-md whitespace-pre-wrap break-words">
                  <p className="text-gray-700">
                    {messageData.messageContent}
                  </p>
                </div>
              </>
            )}
             {/* 处理消息不需要密码，直接显示的情况 */}
            {!messageData.requiresPassword && showMessage && messageData.messageContent && (
                 <>
                 <h2 className="text-xl font-semibold text-gray-800 mb-4">{t('view.title.content') || '消息内容'}</h2>
                 <div className="p-4 bg-gray-50 border border-gray-200 rounded-md whitespace-pre-wrap break-words">
                   <p className="text-gray-700">
                     {messageData.messageContent}
                   </p>
                 </div>
               </>
            )}
            {/* 处理消息已销毁或未找到的特定情况，即使API成功返回但success:false */}
            {showMessage && !messageData.messageContent && !messageData.requiresPassword && (
                <p className="text-lg font-medium text-gray-700">{t('view.error.not_found')}</p>
            )}
          </>
        )}
      </div>
    </Layout>
  );
}
// burn-after-reading-app/app/not-found.js
import Layout from '../components/Layout'; // 引入布局组件

/**
 * 404 页面 - 当访问的资源不存在或已销毁时显示
 */
export default function NotFoundPage() {
  return (
    <Layout>
      <div className="flex flex-col items-center justify-center text-center h-full">
        <h1 className="text-4xl font-bold text-gray-800 mb-4">🤔</h1>
        <h2 className="text-2xl font-semibold text-gray-700 mb-3">信息不存在或已被销毁</h2>
        <p className="text-gray-500">
          您尝试访问的内容可能已被查看后销毁，或者链接已过期。
        </p>
        {/* 可以考虑添加一个返回首页的链接 */}
        {/*
        <a href="/" className="mt-6 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors">
          返回首页
        </a>
        */}
      </div>
    </Layout>
  );
}
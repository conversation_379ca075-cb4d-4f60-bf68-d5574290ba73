// app/i18n.ts
import { createI18nServer } from 'next-international/server';
import { createI18nClient } from 'next-international/client';

export const { getI18n, getScopedI18n, getStaticParams, getCurrentLocale } = createI18nServer({
  'en-US': () => import('../locales/server/en'),
  'zh-CN': () => import('../locales/server/zh-CN'),
});

export const { I18nProviderClient, useI18n, useScopedI18n: useScopedI18nClient, useChangeLocale, useCurrentLocale } = createI18nClient({
  'en-US': () => import('../locales/client/en'),
  'zh-CN': () => import('../locales/client/zh-CN'),
});
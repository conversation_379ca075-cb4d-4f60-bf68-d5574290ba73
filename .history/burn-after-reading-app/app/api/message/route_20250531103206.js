/**
 * @file 处理消息创建相关的API请求
 * @description 该路由文件负责处理创建新阅后即焚消息的POST请求。
 */

/**
 * 处理创建新消息的POST请求。
 * @async
 * @param {Request} request - Next.js的Request对象，包含客户端的请求信息。
 * @returns {Promise<Response>} 一个Promise，解析为Next.js的Response对象。
 *                                成功时返回包含链接ID的JSON响应，失败时返回错误信息。
 */
export async function POST(request) {
  /**
   * @type {object | null} data - 从请求体中解析出的JSON数据。
   */
  let data = null;
  try {
    data = await request.json();
    // 在服务端打印接收到的数据，用于调试
    console.log('接收到的创建消息请求数据:', data);

    // 模拟消息创建逻辑
    const linkId = `mock-link-id-${Date.now()}`;

    // 返回成功的JSON响应
    return Response.json(
      {
        success: true,
        message: '消息创建请求已收到 (模拟)',
        linkId: linkId,
      },
      { status: 200 }
    );
  } catch (error) {
    // 处理JSON解析失败或其他错误
    console.error('处理创建消息请求时发生错误:', error);
    return Response.json(
      {
        success: false,
        error: '无法处理您的请求，数据格式可能不正确。',
      },
      { status: 400 } // 400 Bad Request 通常用于客户端错误，如格式错误的JSON
    );
  }
}
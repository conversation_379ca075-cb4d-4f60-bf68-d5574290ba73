/**
 * @file 处理消息创建相关的API请求
 * @description 该路由文件负责处理创建新阅后即焚消息的POST请求。
 */

import { kv } from '../../../lib/kv'; // 导入 Vercel KV 客户端
import crypto from 'crypto'; // 导入 crypto 用于生成唯一ID

/**
 * 处理创建新消息的POST请求。
 * @async
 * @param {Request} request - Next.js的Request对象，包含客户端的请求信息。
 * @returns {Promise<Response>} 一个Promise，解析为Next.js的Response对象。
 *                                成功时返回包含消息ID和分享链接的JSON响应，失败时返回错误信息。
 */
export async function POST(request) {
  try {
    const body = await request.json();
    const { text, password, destroyOption } = body; // destroyOption 可以是 '1hour', '24hours', 'onRead'

    // 基本的输入验证
    if (!text || typeof text !== 'string' || text.trim() === '') {
      return Response.json(
        { success: false, error: '消息内容不能为空。' },
        { status: 400 }
      );
    }

    const messageId = crypto.randomUUID();
    const createdAt = new Date().toISOString();

    const messageData = {
      text,
      createdAt,
    };

    if (password && typeof password === 'string' && password !== '') {
      messageData.password = password; // 只有在提供密码时才存储
    }

    // 根据产品需求，'onRead' 也是一种销毁条件，但它是在读取时处理，而不是通过TTL
    // 因此，这里我们只为基于时间的销毁条件设置TTL
    messageData.destroyOption = destroyOption || 'onRead'; // 默认为 onRead

    const kvOptions = {};
    if (destroyOption === '1hour') {
      kvOptions.ex = 3600; // 1小时
    } else if (destroyOption === '24hours') {
      kvOptions.ex = 86400; // 24小时
    }
    // 如果是 'onRead' 或其他未明确指定时间的选项，则不设置 ex，消息将永不过期（除非被读取后删除）

    await kv.set(messageId, messageData, kvOptions);

    // 返回成功的JSON响应
    return Response.json(
      {
        success: true,
        message: '消息已成功创建。',
        messageId: messageId,
        shareLink: `/view/${messageId}`, // 构造分享链接
      },
      { status: 201 } // 201 Created 更适合资源创建成功的场景
    );
  } catch (error) {
    console.error('处理创建消息请求时发生错误:', error);
    // 根据错误类型返回更具体的错误信息
    if (error instanceof SyntaxError) {
      return Response.json(
        { success: false, error: '请求数据格式不正确，请确保是有效的JSON。' },
        { status: 400 }
      );
    }
    return Response.json(
      { success: false, error: '服务器内部错误，无法创建消息。' },
      { status: 500 }
    );
  }
}
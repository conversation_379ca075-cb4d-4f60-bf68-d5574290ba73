/**
 * @file 处理根据ID获取消息相关的API请求
 * @description 该路由文件负责处理通过特定ID获取阅后即焚消息的GET请求。
 */

/**
 * 处理根据消息ID获取消息的GET请求。
 * @async
 * @param {Request} request - Next.js的Request对象，包含客户端的请求信息。
 * @param {object} context - 包含路由参数的对象。
 * @param {object} context.params - 路由参数。
 * @param {string} context.params.id - 从URL中提取的消息ID。
 * @returns {Promise<Response>} 一个Promise，解析为Next.js的Response对象。
 *                                成功时根据ID返回相应的模拟消息数据或提示，
 *                                找不到消息时返回404错误。
 */
export async function GET(request, { params }) {
  const messageId = params.id;
  // 在服务端打印请求的消息ID，用于调试
  console.log('请求的消息ID:', messageId);

  // 模拟根据messageId获取消息的逻辑
  if (messageId === 'mock-link-id-valid') {
    /**
     * @description 当messageId为 "mock-link-id-valid" 时，返回此模拟消息内容。
     */
    return Response.json(
      {
        success: true,
        messageContent: "这是一个通过 'mock-link-id-valid' 获取的模拟秘密消息。",
        requiresPassword: false,
      },
      { status: 200 }
    );
  } else if (messageId === 'mock-link-id-password') {
    /**
     * @description 当messageId为 "mock-link-id-password" 时，提示消息需要密码。
     */
    return Response.json(
      {
        success: true,
        requiresPassword: true,
        message: '此消息 (mock-link-id-password) 需要密码才能查看。',
      },
      { status: 200 }
    );
  } else {
    /**
     * @description 对于任何其他messageId，返回消息不存在或已销毁的错误。
     */
    return Response.json(
      { success: false, error: '消息不存在或已销毁。' },
      { status: 404 }
    );
  }
}
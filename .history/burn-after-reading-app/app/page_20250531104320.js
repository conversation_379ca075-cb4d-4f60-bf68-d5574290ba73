// burn-after-reading-app/app/page.js
import { useState } from 'react'; // 引入 React Hooks useState
import Layout from '../components/Layout'; // 引入布局组件

/**
 * @file 首页 - 创建阅后即焚消息的表单页面
 * @module CreateMessagePage
 */

/**
 * 首页组件，用于创建新的阅后即焚消息。
 * @returns {JSX.Element} 首页的JSX元素。
 */
export default function CreateMessagePage() {
  /**
   * @state
   * @property {string} messageContent - 用户输入的文本信息。
   */
  const [messageContent, setMessageContent] = useState('');

  /**
   * @state
   * @property {string} password - 用户设置的可选密码。
   */
  const [password, setPassword] = useState('');

  /**
   * @state
   * @property {string} destroyCondition - 用户选择的销毁条件，默认为 "immediate" (查看后立即销毁)。
   */
  const [destroyCondition, setDestroyCondition] = useState('immediate');

  /**
   * 处理消息内容变化的事件回调函数。
   * @param {React.ChangeEvent<HTMLTextAreaElement>} event - 事件对象。
   */
  const handleMessageChange = (event) => {
    setMessageContent(event.target.value);
  };

  /**
   * 处理密码输入变化的事件回调函数。
   * @param {React.ChangeEvent<HTMLInputElement>} event - 事件对象。
   */
  const handlePasswordChange = (event) => {
    setPassword(event.target.value);
  };

  /**
   * 处理销毁条件选择变化的事件回调函数。
   * @param {React.ChangeEvent<HTMLInputElement>} event - 事件对象。
   */
  const handleDestroyConditionChange = (event) => {
    setDestroyCondition(event.target.value);
  };

  /**
   * 处理表单提交的事件回调函数。
   * 阻止表单默认提交行为，收集表单数据并打印到控制台，然后显示提示信息。
   * @param {React.FormEvent<HTMLFormElement>} event - 事件对象。
   */
  /**
   * 处理表单提交的事件回调函数。
   * 阻止表单默认提交行为，收集表单数据，并异步发送到后端 API。
   * 根据 API 响应结果向用户显示提示信息。
   * @param {React.FormEvent<HTMLFormElement>} event - 事件对象。
   */
  const handleSubmit = async (event) => {
    event.preventDefault(); // 阻止表单默认提交行为

    // 准备发送到 API 的数据
    const payload = {
      messageContent, // 从 state 获取
      password,       // 从 state 获取
      destroyCondition, // 从 state 获取
    };

    try {
      const response = await fetch('/api/message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('API 响应成功:', result);
        alert(`模拟链接已生成！ID: ${result.linkId}。详情请查看控制台。`);
        // 可以在这里添加成功后的其他操作，例如清空表单
        // setMessageContent('');
        // setPassword('');
        // setDestroyCondition('immediate');
      } else {
        // 处理 API 返回的错误状态 (例如 400, 500)
        let errorData;
        try {
          errorData = await response.json();
        } catch (e) {
          // 如果响应体不是 JSON 或为空
          errorData = { message: response.statusText || '创建消息失败，服务器未返回具体错误。' };
        }
        console.error('API 请求失败:', response.status, errorData);
        alert(`创建消息失败：${errorData.message || '请稍后再试。'}`);
      }
    } catch (error) {
      // 处理网络错误或其他在 fetch 过程中发生的异常
      console.error('网络请求错误:', error);
      alert('创建消息失败，请检查您的网络连接并稍后再试。');
    }
  };

  return (
    <Layout>
      <div className="max-w-2xl mx-auto p-4 sm:p-6 lg:p-8 bg-white shadow-lg rounded-lg">
        <h2 className="text-2xl font-semibold text-gray-800 mb-6">创建新的阅后即焚消息</h2>

        {/* 表单开始，绑定 handleSubmit 事件 */}
        <form className="space-y-6" onSubmit={handleSubmit}>
          {/* 文本输入区域 */}
          <div>
            <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
              输入您的消息内容：
            </label>
            <textarea
              id="message"
              name="message"
              rows="10"
              className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="在此输入您的秘密信息..."
              value={messageContent} // 绑定状态
              onChange={handleMessageChange} // 绑定事件处理函数
            ></textarea>
          </div>

          {/* 密码输入区域 */}
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
              设置密码（可选）：
            </label>
            <input
              type="password"
              id="password"
              name="password"
              className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="留空则不设密码"
              value={password} // 绑定状态
              onChange={handlePasswordChange} // 绑定事件处理函数
            />
          </div>

          {/* 销毁条件选择区域 */}
          <fieldset>
            <legend className="block text-sm font-medium text-gray-700 mb-2">选择销毁条件：</legend>
            <div className="space-y-2">
              {/* 查看后立即销毁 */}
              <div className="flex items-center">
                <input
                  id="destroyImmediate"
                  name="destroyCondition"
                  type="radio"
                  value="immediate"
                  className="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"
                  checked={destroyCondition === 'immediate'} // 绑定状态
                  onChange={handleDestroyConditionChange} // 绑定事件处理函数
                />
                <label htmlFor="destroyImmediate" className="ml-2 block text-sm text-gray-900">
                  查看后立即销毁
                </label>
              </div>
              {/* 1小时后销毁 */}
              <div className="flex items-center">
                <input
                  id="destroy1Hour"
                  name="destroyCondition"
                  type="radio"
                  value="1hour"
                  className="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"
                  checked={destroyCondition === '1hour'} // 绑定状态
                  onChange={handleDestroyConditionChange} // 绑定事件处理函数
                />
                <label htmlFor="destroy1Hour" className="ml-2 block text-sm text-gray-900">
                  1小时后销毁
                </label>
              </div>
              {/* 24小时后销毁 */}
              <div className="flex items-center">
                <input
                  id="destroy24Hours"
                  name="destroyCondition"
                  type="radio"
                  value="24hours"
                  className="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"
                  checked={destroyCondition === '24hours'} // 绑定状态
                  onChange={handleDestroyConditionChange} // 绑定事件处理函数
                />
                <label htmlFor="destroy24Hours" className="ml-2 block text-sm text-gray-900">
                  24小时后销毁
                </label>
              </div>
            </div>
          </fieldset>

          {/* 提交按钮 */}
          <div>
            <button
              type="submit" // type="submit" 会触发 form 的 onSubmit 事件
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              生成分享链接
            </button>
          </div>
        </form>
      </div>
    </Layout>
  );
}

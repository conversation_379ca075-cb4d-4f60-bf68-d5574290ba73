// burn-after-reading-app/app/not-found.js
import Layout from '../components/Layout';
import { getI18n } from './i18n'; // 导入 getI18n
import Link from 'next/link'; // 导入 Link 组件用于导航

export default async function NotFoundPage() {
  const t = await getI18n(); // 获取翻译函数

  return (
    <Layout>
      <div className="flex flex-col items-center justify-center text-center h-screen px-4"> {/* 使用 h-screen 并添加水平内边距 */}
        <div className="max-w-md"> {/* 限制内容最大宽度 */}
          <h1 className="text-6xl font-bold text-indigo-600 mb-4">404</h1>
          <h2 className="text-3xl font-semibold text-gray-800 mb-3">
            {t('notfound.title')}
          </h2>
          <p className="text-gray-600 mb-8">
            {t('notfound.message')}
          </p>
          <Link href="/" legacyBehavior>
            <a className="px-6 py-3 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors text-lg font-medium">
              {t('notfound.button.home')}
            </a>
          </Link>
        </div>
      </div>
    </Layout>
  );
}
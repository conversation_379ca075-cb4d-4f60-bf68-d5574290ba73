// burn-after-reading-app/app/page.js
import Layout from '../components/Layout'; // 引入布局组件

/**
 * 首页 - 创建阅后即焚消息的表单页面
 */
export default function CreateMessagePage() {
  return (
    <Layout>
      <div className="max-w-2xl mx-auto p-4 sm:p-6 lg:p-8 bg-white shadow-lg rounded-lg">
        <h2 className="text-2xl font-semibold text-gray-800 mb-6">创建新的阅后即焚消息</h2>

        <form className="space-y-6">
          {/* 文本输入区域 */}
          <div>
            <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
              输入您的消息内容：
            </label>
            <textarea
              id="message"
              name="message"
              rows="10"
              className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="在此输入您的秘密信息..."
            ></textarea>
          </div>

          {/* 密码输入区域 */}
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
              设置密码（可选）：
            </label>
            <input
              type="password"
              id="password"
              name="password"
              className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="留空则不设密码"
            />
          </div>

          {/* 销毁条件选择区域 */}
          <fieldset>
            <legend className="block text-sm font-medium text-gray-700 mb-2">选择销毁条件：</legend>
            <div className="space-y-2">
              <div className="flex items-center">
                <input
                  id="destroyImmediate"
                  name="destroyCondition"
                  type="radio"
                  value="immediate"
                  className="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"
                  defaultChecked
                />
                <label htmlFor="destroyImmediate" className="ml-2 block text-sm text-gray-900">
                  查看后立即销毁
                </label>
              </div>
              <div className="flex items-center">
                <input
                  id="destroy1Hour"
                  name="destroyCondition"
                  type="radio"
                  value="1hour"
                  className="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"
                />
                <label htmlFor="destroy1Hour" className="ml-2 block text-sm text-gray-900">
                  1小时后销毁
                </label>
              </div>
              <div className="flex items-center">
                <input
                  id="destroy24Hours"
                  name="destroyCondition"
                  type="radio"
                  value="24hours"
                  className="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"
                />
                <label htmlFor="destroy24Hours" className="ml-2 block text-sm text-gray-900">
                  24小时后销毁
                </label>
              </div>
            </div>
          </fieldset>

          {/* 提交按钮 */}
          <div>
            <button
              type="submit"
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              生成分享链接
            </button>
          </div>
        </form>
      </div>
    </Layout>
  );
}

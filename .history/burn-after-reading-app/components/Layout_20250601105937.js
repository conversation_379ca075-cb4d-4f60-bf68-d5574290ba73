// burn-after-reading-app/components/Layout.js
'use client'; // 声明为客户端组件以使用 hook

import React from 'react';
import { useI18n } from '../app/i18n'; // 导入 i18n hook

export default function Layout({ children }) {
  const t = useI18n(); // 获取翻译函数
  const currentYear = new Date().getFullYear();

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <header className="bg-gray-800 text-white p-4 shadow-md rounded-b-lg">
        <div className="container mx-auto">
          {/* 使用翻译函数获取标题，假设 'default.title' 在服务器或客户端翻译文件中定义 */}
          {/* 如果只在服务器端定义，客户端组件可能无法直接访问，需要调整翻译文件结构或传递 props */}
          {/* 为简单起见，我们假设 'default.title' 在客户端翻译文件中也存在，或者 Layout 只用于客户端页面 */}
          {/* 更稳妥的做法是，如果 Layout 可能被服务器组件直接使用，则应考虑其国际化方式 */}
          {/* 但由于我们已将其标记为 'use client'，此处使用 useI18n 是合适的 */}
          <h1 className="text-2xl font-semibold">{t('default.title')}</h1>
        </div>
      </header>

      <main className="flex-grow container mx-auto p-4 sm:p-6 lg:p-8">
        {children}
      </main>

      <footer className="bg-gray-200 text-gray-700 p-4 text-center rounded-t-lg mt-auto">
        {/* 使用翻译函数获取页脚文本 */}
        <p>&copy; {currentYear} {t('layout.footer.text')}</p>
      </footer>
    </div>
  );
}
// burn-after-reading-app/components/Layout.js
import React from 'react';

/**
 * 网站通用布局组件
 * @param {object} props - 组件属性
 * @param {React.ReactNode} props.children - 需要在主要内容区域渲染的子组件
 */
export default function Layout({ children }) {
  const currentYear = new Date().getFullYear(); // 动态获取当前年份

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      {/* 页眉区域 */}
      <header className="bg-gray-800 text-white p-4 shadow-md rounded-b-lg">
        <div className="container mx-auto">
          <h1 className="text-2xl font-semibold">阅后即焚</h1>
        </div>
      </header>

      {/* 主要内容区域 */}
      <main className="flex-grow container mx-auto p-4 sm:p-6 lg:p-8">
        {children}
      </main>

      {/* 页脚区域 */}
      <footer className="bg-gray-200 text-gray-700 p-4 text-center rounded-t-lg mt-auto">
        <p>&copy; {currentYear} 阅后即焚</p>
      </footer>
    </div>
  );
}
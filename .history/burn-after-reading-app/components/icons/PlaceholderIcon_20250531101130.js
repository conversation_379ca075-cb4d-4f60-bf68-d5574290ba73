// burn-after-reading-app/components/icons/PlaceholderIcon.js

/**
 * 一个占位符 SVG 图标组件
 * @param {object} props - 组件属性
 * @param {string} [props.className="w-6 h-6"] - 图标的 CSS 类名，默认为 "w-6 h-6"
 */
export default function PlaceholderIcon({ className = "w-6 h-6" }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className={className}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
      <path strokeLinecap="round" strokeLinejoin="round" d="M15.91 11.672a.375.375 0 0 1 0 .656l-5.603 3.113a.375.375 0 0 1-.557-.328V8.887c0-.286.307-.466.557-.327l5.603 3.112Z" />
    </svg>
  );
}
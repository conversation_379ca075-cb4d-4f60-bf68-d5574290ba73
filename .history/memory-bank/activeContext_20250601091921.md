# 动态上下文记录 (Active Context)

本文件由当前活动的子任务动态写入，用于记录其详细的工作过程、思考、数据分析、遇到的问题和解决方案等。

---
*NexusCore 已于 2025-05-31 上午11:23 清空此文件，为新的子任务做准备。*
---

## TASK-I18N-RESEARCH-001: Next.js App Router i18n 技术选型调研

**任务启动时间**: 2025-05-31 11:26 (已于 2025-06-01 09:17 继续)

**目标**: 调研并推荐一个适用于 Next.js App Router 的国际化 (i18n) 解决方案。

**背景**:
*   项目技术栈: Next.js (最新稳定版, App Router)。
*   需求: 初期支持中文 (`zh-CN` - 默认) 和英文 (`en-US`)。
*   [`productContext.md`](memory-bank/productContext.md:51) 提及 `next-i18next`，但需关注其与 App Router 的兼容性并调研更优方案。

**调研计划**:
1.  调研 `next-international`。(已完成)
2.  调研 Next.js 13/14+ 内建 i18n 方案。(进行中)
3.  调研其他备选方案 (包括 `next-i18next` 在 App Router 下的现状)。
4.  对比分析与评估。
5.  产出调研报告和推荐方案。

---

### 1. 调研 `next-international`

**调研时间**: 2025-06-01 09:18

**概述**: `next-international` 是一个为 Next.js App Router 设计的、注重类型安全的国际化库。它旨在提供一种现代且开发者友好的方式来处理多语言应用。

**评估**:

*   **与 Next.js App Router 的兼容性和集成顺畅度**:
    *   **高度兼容**: 专门为 App Router 构建，充分利用其特性，如 Server Components, Client Components, 和中间件。
    *   **集成**: 通常涉及创建区域设置 (locales) 目录和定义翻译文件 (如 JSON 或 TS 文件)。集成过程相对直接，文档清晰。
    *   路由策略通常基于动态段 `[locale]`，例如 `/en/about`, `/zh/about`。

*   **易用性和学习曲线**:
    *   **易用性**: API 设计简洁，强调类型安全，有助于减少运行时错误。
    *   **学习曲线**: 对于熟悉 Next.js App Router 和 TypeScript 的开发者来说，学习曲线相对平缓。类型系统提供了良好的引导。

*   **社区活跃度和支持情况**:
    *   **社区**: 相对较新，社区规模可能不如老牌库 (如 `next-i18next`) 大，但正在积极增长。
    *   **支持**: 维护者活跃，GitHub issues 和 discussions 响应及时。

*   **主要特性**:
    *   **类型安全**: 核心优势。确保所有翻译键和区域设置在编译时都是类型安全的，减少错误。
    *   **Server Components 支持**: 良好支持在 Server Components 中获取和使用翻译。
    *   **Client Components 支持**: 提供 Provider 和 hooks (`useScopedI18n`, `useI18n`) 供 Client Components 使用。
    *   **作用域翻译 (Scoped Translations)**: 允许为特定组件或页面定义翻译，避免全局命名空间冲突。
    *   **动态加载区域设置**: 可以按需加载语言文件，优化初始加载性能。
    *   **ICU Message Format**: 支持 ICU 消息格式，可以处理复数、性别等复杂翻译场景 (通常通过集成如 `intl-messageformat` 的库实现)。
    *   **性能**: 设计上考虑了性能，例如通过代码分割和按需加载区域设置。
    *   **中间件集成**: 通常使用 Next.js 中间件来检测和设置当前区域设置。

*   **维护成本和长期可行性**:
    *   **维护成本**: 由于其现代化的设计和对 App Router 的原生支持，长期维护成本预计较低。类型安全有助于减少 bug。
    *   **长期可行性**: 作为一个专注于 App Router 的解决方案，其发展方向与 Next.js 的演进保持一致，具有较好的长期可行性。

**初步优缺点**:

*   **优点**:
    *   👍 专为 App Router 设计，兼容性好。
    *   👍 强大的类型安全特性，提升开发体验和代码质量。
    *   👍 对 Server Components 和 Client Components 均有良好支持。
    *   👍 API 设计现代且易于理解。
    *   👍 支持作用域翻译和动态加载。
*   **缺点**:
    *   👎 社区规模和生态系统相较于老牌库可能稍小。
    *   👎 对于不熟悉 TypeScript 或追求极简配置的团队，可能会有一点额外的学习成本。

**(调研笔记结束)**

---

### 2. 调研 Next.js 13/14+ 内建 i18n 方案

**调研时间**: 2025-06-01 09:19

**概述**: Next.js 提供了国际化路由 (Internationalized Routing) 功能，允许开发者通过配置和利用 App Router 的特性 (如动态路由段和中间件) 来构建多语言应用。这通常不依赖于特定的第三方 i18n 库来处理路由和语言检测，但翻译管理本身仍可能需要辅助工具或自定义实现。

**评估**:

*   **与 Next.js App Router 的兼容性和集成顺畅度**:
    *   **原生兼容**: 作为 Next.js 的一部分，兼容性是最好的。直接利用框架提供的能力。
    *   **集成**:
        *   在 `next.config.mjs` 中配置 `i18n` 对象，定义 `locales` (支持的语言列表) 和 `defaultLocale`。
        *   Next.js 会自动处理基于路径前缀 (`/en/page`, `/zh-CN/page`) 或域名 (较少见于此场景) 的路由。
        *   可以使用 `middleware.ts` (或 `.js`) 来实现更复杂的区域设置检测逻辑 (例如从 `Accept-Language` 头部、cookie 或用户偏好设置) 和重定向。
        *   页面和布局通常需要通过 `params` (例如 `params.lang`) 接收当前语言环境，然后据此加载对应的翻译内容。

*   **易用性和学习曲线**:
    *   **易用性**: 对于路由和语言检测部分，Next.js 官方文档提供了清晰的指导。然而，翻译内容的管理 (加载、插值、复数处理等) 需要开发者自行实现或集成轻量级库。
    *   **学习曲线**: 理解 Next.js 的 i18n 路由配置和中间件用法是关键。如果需要手动管理翻译，则学习曲线会相应增加。

*   **社区活跃度和支持情况**:
    *   **社区**: Next.js 拥有庞大且活跃的社区。关于内建 i18n 功能的讨论和示例丰富。
    *   **支持**: Vercel 官方提供支持和文档。

*   **主要特性**:
    *   **路由**: 核心是国际化路由，自动处理 URL 中的语言前缀。
    *   **语言检测**: 可以通过中间件灵活实现。
    *   **Server Components 和 Client Components**: 语言环境可以通过 `params` 传递给 Server Components。对于 Client Components，可能需要通过 Context 或 props 传递语言和翻译函数。
    *   **翻译管理**: Next.js 本身不直接提供完整的翻译管理系统 (如 `i18next` 实例或类型安全的翻译函数)。开发者通常需要：
        *   自行组织翻译文件 (如 JSON)。
        *   实现加载和使用翻译的逻辑。
        *   考虑集成小型库来辅助处理插值、复数等。
    *   **性能**: 路由层面性能良好。翻译加载性能取决于实现方式。
    *   **ICU Message Format**: 原生不支持，需要自行集成相关库。

*   **维护成本和长期可行性**:
    *   **维护成本**: 路由部分由 Next.js 维护，成本低。翻译管理部分的维护成本取决于自定义实现的复杂度和所选辅助库的质量。
    *   **长期可行性**: 作为 Next.js 的核心功能，长期可行性有保障。

**初步优缺点**:

*   **优点**:
    *   👍 与 Next.js App Router 深度集成，原生支持。
    *   👍 官方文档支持，社区资源丰富。
    *   👍 路由和语言检测机制灵活且强大 (尤其结合中间件)。
    *   👍 无需引入大型第三方 i18n 运行时库 (如果仅依赖内建路由)。
*   **缺点**:
    *   👎 不提供开箱即用的翻译管理功能 (如类型安全的 t 函数、命名空间、ICU 支持等)，需要自行实现或集成小型库，增加了初始设置和维护的复杂度。
    *   👎 对于复杂的翻译需求 (如 ICU 格式、上下文翻译)，纯手动管理可能变得繁琐且容易出错。
    *   👎 开发者体验可能不如专门的 i18n 库流畅，尤其是在翻译键的类型安全和编辑器智能提示方面。

**(调研笔记结束)**
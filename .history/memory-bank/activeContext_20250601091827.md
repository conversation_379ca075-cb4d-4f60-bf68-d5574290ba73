# 动态上下文记录 (Active Context)

本文件由当前活动的子任务动态写入，用于记录其详细的工作过程、思考、数据分析、遇到的问题和解决方案等。

---
*NexusCore 已于 2025-05-31 上午11:23 清空此文件，为新的子任务做准备。*
---

## TASK-I18N-RESEARCH-001: Next.js App Router i18n 技术选型调研

**任务启动时间**: 2025-05-31 11:26 (已于 2025-06-01 09:17 继续)

**目标**: 调研并推荐一个适用于 Next.js App Router 的国际化 (i18n) 解决方案。

**背景**:
*   项目技术栈: Next.js (最新稳定版, App Router)。
*   需求: 初期支持中文 (`zh-CN` - 默认) 和英文 (`en-US`)。
*   [`productContext.md`](memory-bank/productContext.md:51) 提及 `next-i18next`，但需关注其与 App Router 的兼容性并调研更优方案。

**调研计划**:
1.  调研 `next-international`。(进行中)
2.  调研 Next.js 13/14+ 内建 i18n 方案。
3.  调研其他备选方案 (包括 `next-i18next` 在 App Router 下的现状)。
4.  对比分析与评估。
5.  产出调研报告和推荐方案。

---

### 1. 调研 `next-international`

**调研时间**: 2025-06-01 09:18

**概述**: `next-international` 是一个为 Next.js App Router 设计的、注重类型安全的国际化库。它旨在提供一种现代且开发者友好的方式来处理多语言应用。

**评估**:

*   **与 Next.js App Router 的兼容性和集成顺畅度**:
    *   **高度兼容**: 专门为 App Router 构建，充分利用其特性，如 Server Components, Client Components, 和中间件。
    *   **集成**: 通常涉及创建区域设置 (locales) 目录和定义翻译文件 (如 JSON 或 TS 文件)。集成过程相对直接，文档清晰。
    *   路由策略通常基于动态段 `[locale]`，例如 `/en/about`, `/zh/about`。

*   **易用性和学习曲线**:
    *   **易用性**: API 设计简洁，强调类型安全，有助于减少运行时错误。
    *   **学习曲线**: 对于熟悉 Next.js App Router 和 TypeScript 的开发者来说，学习曲线相对平缓。类型系统提供了良好的引导。

*   **社区活跃度和支持情况**:
    *   **社区**: 相对较新，社区规模可能不如老牌库 (如 `next-i18next`) 大，但正在积极增长。
    *   **支持**: 维护者活跃，GitHub issues 和 discussions 响应及时。

*   **主要特性**:
    *   **类型安全**: 核心优势。确保所有翻译键和区域设置在编译时都是类型安全的，减少错误。
    *   **Server Components 支持**: 良好支持在 Server Components 中获取和使用翻译。
    *   **Client Components 支持**: 提供 Provider 和 hooks (`useScopedI18n`, `useI18n`) 供 Client Components 使用。
    *   **作用域翻译 (Scoped Translations)**: 允许为特定组件或页面定义翻译，避免全局命名空间冲突。
    *   **动态加载区域设置**: 可以按需加载语言文件，优化初始加载性能。
    *   **ICU Message Format**: 支持 ICU 消息格式，可以处理复数、性别等复杂翻译场景 (通常通过集成如 `intl-messageformat` 的库实现)。
    *   **性能**: 设计上考虑了性能，例如通过代码分割和按需加载区域设置。
    *   **中间件集成**: 通常使用 Next.js 中间件来检测和设置当前区域设置。

*   **维护成本和长期可行性**:
    *   **维护成本**: 由于其现代化的设计和对 App Router 的原生支持，长期维护成本预计较低。类型安全有助于减少 bug。
    *   **长期可行性**: 作为一个专注于 App Router 的解决方案，其发展方向与 Next.js 的演进保持一致，具有较好的长期可行性。

**初步优缺点**:

*   **优点**:
    *   👍 专为 App Router 设计，兼容性好。
    *   👍 强大的类型安全特性，提升开发体验和代码质量。
    *   👍 对 Server Components 和 Client Components 均有良好支持。
    *   👍 API 设计现代且易于理解。
    *   👍 支持作用域翻译和动态加载。
*   **缺点**:
    *   👎 社区规模和生态系统相较于老牌库可能稍小。
    *   👎 对于不熟悉 TypeScript 或追求极简配置的团队，可能会有一点额外的学习成本。

**(调研笔记结束)**
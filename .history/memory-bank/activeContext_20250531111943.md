# 动态上下文记录 (Active Context)

本文件由当前活动的子任务动态写入，用于记录其详细的工作过程、思考、数据分析、遇到的问题和解决方案等。

---
*NexusCore 已于 2025-05-31 上午11:12 清空此文件，为新的子任务做准备。*
---
*   [2025-05-31 11:16:18] - 开始执行任务 `TASK-API-KV-IMPL-001`。
*   [2025-05-31 11:16:18] - 目标：修改 `POST /api/message` ([`burn-after-reading-app/app/api/message/route.js`](burn-after-reading-app/app/api/message/route.js:0)) 以使用 Vercel KV。
*   [2025-05-31 11:16:18] - 已读取 [`burn-after-reading-app/app/api/message/route.js`](burn-after-reading-app/app/api/message/route.js:0) 的当前内容。
*   [2025-05-31 11:16:18] - **计划修改 `burn-after-reading-app/app/api/message/route.js`**:
    *   导入 `kv` from `../../../lib/kv` (注意相对路径的调整，因为 API 路由在 `app/api/message/` 下，而 `kv.js` 在 `lib/` 下)。
    *   导入 `crypto`。
    *   从请求体中解析 `text`, `password` (可选), 和 `destroyOption` (可选，用于TTL)。
    *   生成唯一 `messageId` 使用 `crypto.randomUUID()`。
    *   创建 `messageData` 对象，包含 `text`, `password` (如果提供), `createdAt: new Date().toISOString()`。
    *   根据 `destroyOption` 设置 Vercel KV 的 `ex` (过期时间，以秒为单位):
        *   '1hour': 3600
        *   '24hours': 86400
        *   如果 `destroyOption` 是 'onRead' 或未提供，则不设置 `ex`。
    *   使用 `await kv.set(messageId, messageData, options)` 存储数据。
    *   返回成功响应，包含 `messageId` 和 `shareLink: \`/view/${messageId}\``。
    *   添加错误处理。
*   [2025-05-31 11:17:15] - 成功修改并保存了 [`burn-after-reading-app/app/api/message/route.js`](burn-after-reading-app/app/api/message/route.js:0)。
*   [2025-05-31 11:17:15] - 下一步：修改 `GET /api/message/[id]` ([`burn-after-reading-app/app/api/message/[id]/route.js`](burn-after-reading-app/app/api/message/[id]/route.js:0))。
*   [2025-05-31 11:18:21] - 已读取 [`burn-after-reading-app/app/api/message/[id]/route.js`](burn-after-reading-app/app/api/message/[id]/route.js:0) 的当前内容。
*   [2025-05-31 11:18:21] - **计划修改 `burn-after-reading-app/app/api/message/[id]/route.js`**:
    *   导入 `kv` from `../../../../lib/kv`。
    *   从 `params` 获取 `messageId`。
    *   使用 `await kv.get(messageId)` 检索消息。
    *   如果消息为 `null`，返回 404。
    *   **密码处理**:
        *   检查 `request.headers.get('x-message-password')` 或从请求体中获取密码 (需要确定前端如何传递密码，暂时假设通过 header `x-message-password`)。
        *   如果消息有 `password` 字段：
            *   如果请求中没有提供密码，返回 `{ success: true, requiresPassword: true }`。
            *   如果提供了密码，与存储的密码比较。
            *   如果不匹配，返回 401 或 403。
    *   **消息销毁**:
        *   如果消息的 `destroyOption` 是 `onRead` (或默认值)，并且密码验证通过（或无密码），则在返回消息内容后，使用 `await kv.del(messageId)` 删除消息。
    *   返回消息内容 `{ success: true, messageContent: message.text, requiresPassword: false }`。
    *   添加错误处理。
*   [2025-05-31 11:19:15] - 成功修改并保存了 [`burn-after-reading-app/app/api/message/[id]/route.js`](burn-after-reading-app/app/api/message/[id]/route.js:0)。
*   [2025-05-31 11:19:15] - 任务 `TASK-API-KV-IMPL-001` 的核心编码工作已完成。准备向用户确认。
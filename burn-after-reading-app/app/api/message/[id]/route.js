/**
 * @file 处理根据ID获取消息相关的API请求
 * @description 该路由文件负责处理通过特定ID获取阅后即焚消息的GET请求。
 */

import { kv } from '../../../../lib/kv'; // 导入 Vercel KV 客户端

/**
 * 处理根据消息ID获取消息的GET请求。
 * @async
 * @param {Request} request - Next.js的Request对象，包含客户端的请求信息。
 * @param {object} context - 包含路由参数的对象。
 * @param {object} context.params - 路由参数。
 * @param {string} context.params.id - 从URL中提取的消息ID。
 * @returns {Promise<Response>} 一个Promise，解析为Next.js的Response对象。
 */
export async function GET(request, { params }) {
  const messageId = params.id;

  if (!messageId) {
    return Response.json(
      { success: false, error: '未提供消息ID。' },
      { status: 400 }
    );
  }

  try {
    const message = await kv.get(messageId);

    if (!message) {
      return Response.json(
        { success: false, error: '消息不存在或已销毁。' },
        { status: 404 }
      );
    }

    // 处理密码保护
    if (message.password) {
      const providedPassword = request.headers.get('x-message-password');

      if (!providedPassword) {
        // 告知前端需要密码，但不返回消息内容
        return Response.json(
          {
            success: true, // 请求本身是成功的，只是需要额外操作
            requiresPassword: true,
            message: '此消息受密码保护，请提供密码。',
          },
          { status: 200 } // 或者 401 如果认为未授权是更合适的初始状态
        );
      }

      if (providedPassword !== message.password) {
        return Response.json(
          { success: false, error: '密码不正确。' },
          { status: 401 } // 401 Unauthorized
        );
      }
      // 如果密码正确，则继续执行后续逻辑
    }

    // 提取消息内容用于返回
    const messageContent = message.text;

    // 处理“首次查看后销毁” (destroyOption === 'onRead')
    // 注意: 产品需求中提到 "destroyOption" 可能的值，我们在创建时已存入 message 对象
    if (message.destroyOption === 'onRead') {
      await kv.del(messageId); // 在返回消息后立即删除
    }

    return Response.json(
      {
        success: true,
        messageContent: messageContent,
        requiresPassword: false, // 明确告知密码已验证（或无密码）
      },
      { status: 200 }
    );
  } catch (error) {
    console.error(`处理获取消息 ${messageId} 时发生错误:`, error);
    return Response.json(
      { success: false, error: '服务器内部错误，无法获取消息。' },
      { status: 500 }
    );
  }
}
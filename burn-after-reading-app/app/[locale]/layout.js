import { Geist, <PERSON>eist_Mono } from "next/font/google";
import "../globals.css"; // 保持对全局CSS的引用，注意路径调整
import { I18nProviderClient } from "../i18n"; // 调整路径以指向 app/i18n.ts

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

// 元数据可以考虑在 page.js 中针对不同语言动态生成，或在此处提供一个通用版本
// 为了简单起见，暂时保留原来的元数据，后续可以根据需要国际化
export const metadata = {
  title: "Burn After Reading", // 更新一个更合适的标题
  description: "Securely share self-destructing messages.", // 更新描述
};

export default function RootLayout({ children, params: { locale } }) {
  return (
    <I18nProviderClient locale={locale}>
      <html lang={locale}>
        <body
          className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        >
          {children}
        </body>
      </html>
    </I18nProviderClient>
  );
}
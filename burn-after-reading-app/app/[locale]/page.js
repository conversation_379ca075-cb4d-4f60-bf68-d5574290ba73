// burn-after-reading-app/app/[locale]/page.js
'use client'; // 声明为客户端组件

import { useState } from 'react';
import Layout from '../../components/Layout'; // 调整Layout组件的导入路径
import { useI18n } from '../i18n'; // 导入 useI18n hook

export default function CreateMessagePage() {
  const t = useI18n(); // 获取翻译函数
  const [messageContent, setMessageContent] = useState('');
  const [password, setPassword] = useState('');
  const [destroyCondition, setDestroyCondition] = useState('first_view'); // 对应翻译键
  const [generatedLink, setGeneratedLink] = useState('');
  const [isCopied, setIsCopied] = useState(false);

  const handleMessageChange = (event) => {
    setMessageContent(event.target.value);
  };

  const handlePasswordChange = (event) => {
    setPassword(event.target.value);
  };

  const handleDestroyConditionChange = (event) => {
    setDestroyCondition(event.target.value);
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setGeneratedLink(''); // 重置链接
    setIsCopied(false); // 重置复制状态

    const payload = {
      messageContent,
      password,
      destroyCondition,
    };

    try {
      const response = await fetch('/api/message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('API 响应成功:', result);
        // 构建完整的查看链接，假设部署在根路径
        const viewLink = `${window.location.origin}/view/${result.linkId}`;
        setGeneratedLink(viewLink);
        // 清空表单可以根据产品需求决定是否执行
        // setMessageContent('');
        // setPassword('');
        // setDestroyCondition('first_view');
      } else {
        let errorData;
        try {
          errorData = await response.json();
        } catch (e) {
          errorData = { message: response.statusText || t('view.error.generic') };
        }
        console.error('API 请求失败:', response.status, errorData);
        alert(`${t('view.error.generic')}: ${errorData.message || ''}`);
        setGeneratedLink('');
      }
    } catch (error) {
      console.error('网络请求错误:', error);
      alert(t('view.error.generic'));
      setGeneratedLink('');
    }
  };

  const handleCopyLink = () => {
    if (generatedLink) {
      navigator.clipboard.writeText(generatedLink)
        .then(() => {
          setIsCopied(true);
          setTimeout(() => setIsCopied(false), 2000); // 2秒后重置复制状态
        })
        .catch(err => {
          console.error('无法复制链接: ', err);
          alert('无法复制链接，请手动复制。');
        });
    }
  };

  return (
    <Layout>
      <div className="max-w-2xl mx-auto p-4 sm:p-6 lg:p-8 bg-white shadow-lg rounded-lg">
        <h2 className="text-2xl font-semibold text-gray-800 mb-6">{t('home.title')}</h2>

        <form className="space-y-6" onSubmit={handleSubmit}>
          <div>
            <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
              {t('home.label.message')}
            </label>
            <textarea
              id="message"
              name="message"
              rows="10"
              className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder={t('home.placeholder.message')}
              value={messageContent}
              onChange={handleMessageChange}
              required
            ></textarea>
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
              {t('home.label.password')}
            </label>
            <input
              type="password"
              id="password"
              name="password"
              className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder={t('home.placeholder.password')}
              value={password}
              onChange={handlePasswordChange}
            />
          </div>

          <fieldset>
            <legend className="block text-sm font-medium text-gray-700 mb-2">{t('home.label.destroy')}</legend>
            <div className="space-y-2">
              <div className="flex items-center">
                <input
                  id="destroyFirstView"
                  name="destroyCondition"
                  type="radio"
                  value="first_view" // 对应翻译键中的 'first_view'
                  className="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"
                  checked={destroyCondition === 'first_view'}
                  onChange={handleDestroyConditionChange}
                />
                <label htmlFor="destroyFirstView" className="ml-2 block text-sm text-gray-900">
                  {t('home.option.first_view')}
                </label>
              </div>
              <div className="flex items-center">
                <input
                  id="destroy1Hour"
                  name="destroyCondition"
                  type="radio"
                  value="one_hour" // 对应翻译键
                  className="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"
                  checked={destroyCondition === 'one_hour'}
                  onChange={handleDestroyConditionChange}
                />
                <label htmlFor="destroy1Hour" className="ml-2 block text-sm text-gray-900">
                  {t('home.option.one_hour')}
                </label>
              </div>
              <div className="flex items-center">
                <input
                  id="destroy24Hours"
                  name="destroyCondition"
                  type="radio"
                  value="twenty_four_hours" // 对应翻译键
                  className="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"
                  checked={destroyCondition === 'twenty_four_hours'}
                  onChange={handleDestroyConditionChange}
                />
                <label htmlFor="destroy24Hours" className="ml-2 block text-sm text-gray-900">
                  {t('home.option.twenty_four_hours')}
                </label>
              </div>
            </div>
          </fieldset>

          <div>
            <button
              type="submit"
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              {t('home.button.create')}
            </button>
          </div>
        </form>

        {generatedLink && (
          <div className="mt-6 p-4 bg-gray-50 rounded-md">
            <p className="text-sm font-medium text-gray-700">链接已生成:</p>
            <div className="mt-2 flex items-center space-x-2">
              <input
                type="text"
                value={generatedLink}
                readOnly
                className="flex-grow block w-full px-3 py-2 bg-gray-100 border border-gray-300 rounded-md shadow-sm sm:text-sm cursor-not-allowed"
              />
              <button
                onClick={handleCopyLink}
                className="py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                {isCopied ? t('home.link.copied') : t('home.link.copy')}
              </button>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
}
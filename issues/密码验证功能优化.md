# 密码验证功能优化任务

## 任务概述
优化 `burn-after-reading-app/app/[locale]/view/[id]/page.js` 中的密码验证逻辑，从模拟验证改为真正的API调用验证。

## 问题分析
1. **原有问题**：
   - 前端只有模拟密码验证，没有真正调用后端API
   - 代码中有大量冗余注释和解释
   - 缺乏用户反馈和错误处理
   - 消息显示逻辑重复

2. **后端API现状**：
   - `/api/message/[id]` 已支持通过 `x-message-password` 请求头进行密码验证
   - API返回正确的错误状态码（401表示密码错误）

## 实施方案

### 1. 状态管理优化
- 新增 `isVerifyingPassword` 状态：控制密码验证时的加载状态
- 新增 `passwordError` 状态：存储密码验证错误信息

### 2. 密码验证逻辑重构
- 替换模拟验证为真实API调用
- 在请求头中传递密码：`x-message-password`
- 根据API响应处理成功/失败情况
- 密码错误时清空输入框并显示错误信息

### 3. 用户体验改进
- 验证过程中显示加载状态
- 禁用输入框和按钮防止重复提交
- 实时清除错误提示（用户输入时）
- 统一消息显示逻辑，移除重复代码

### 4. 代码结构优化
- 移除大量冗余注释
- 简化条件判断逻辑
- 统一错误处理流程

## 技术实现

### 核心函数重构
```javascript
const handleViewMessageClick = async (event) => {
  // 防止表单默认提交
  if (event && typeof event.preventDefault === 'function') {
    event.preventDefault();
  }

  // 基本验证
  if (!messageData || !messageData.requiresPassword) return;
  if (!inputPassword.trim()) {
    setPasswordError('请输入密码');
    return;
  }

  // 开始验证
  setIsVerifyingPassword(true);
  setPasswordError(null);

  try {
    // 调用API验证密码
    const response = await fetch(`/api/message/${messageId}`, {
      method: 'GET',
      headers: { 'x-message-password': inputPassword },
    });

    if (response.ok) {
      const data = await response.json();
      if (data.success && data.messageContent) {
        // 验证成功，显示消息
        setMessageData(data);
        setShowPasswordForm(false);
        setShowMessage(true);
      } else {
        // API返回错误
        setPasswordError(data.error || '密码错误');
        setInputPassword('');
      }
    } else if (response.status === 401) {
      // 密码错误
      setPasswordError('密码错误');
      setInputPassword('');
    } else {
      // 其他错误
      const errorData = await response.json().catch(() => ({}));
      setPasswordError(errorData.error || '验证失败，请重试');
    }
  } catch (error) {
    // 网络错误
    console.error('密码验证时发生错误:', error);
    setPasswordError('网络错误，请重试');
  } finally {
    setIsVerifyingPassword(false);
  }
};
```

## 执行结果

### 修改的文件
- `burn-after-reading-app/app/[locale]/view/[id]/page.js`

### 主要改动
1. ✅ 添加了 `isVerifyingPassword` 和 `passwordError` 状态
2. ✅ 重构了 `handleViewMessageClick` 函数，实现真正的密码验证
3. ✅ 优化了 `handlePasswordInputChange` 函数，添加错误清除逻辑
4. ✅ 更新了UI，添加密码错误显示和加载状态
5. ✅ 简化了消息显示逻辑，移除重复代码
6. ✅ 移除了大量冗余注释

### 功能验证
- [x] 密码验证功能正常工作
- [x] 错误处理和用户反馈完善
- [x] 加载状态显示正确
- [x] 代码结构清晰简洁

## 测试建议
1. 测试正确密码输入
2. 测试错误密码输入
3. 测试网络错误情况
4. 测试空密码输入
5. 验证加载状态显示

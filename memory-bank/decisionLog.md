# 项目决策日志

本文件记录项目开发过程中的关键技术选型、架构决策等。

## 2025-05-31

*   **决策**: 选择 Vercel KV 作为阅后即焚应用的消息临时存储方案。
    *   **原因**:
        *   与 Next.js 和 Vercel 平台集成良好。
        *   适合无服务器、临时性数据存储需求。
        *   能够支持可靠的数据清除机制，符合产品需求中对安全销毁的要求。
        *   产品需求文档 ([`productContext.md`](memory-bank/productContext.md:32)) 中明确提及 Vercel KV 作为备选方案之一。
    *   **记录人**: 🧠 NexusCore
---
### Decision
[2025-06-01 09:45:00] - 选择 `next-international` 作为项目国际化 (i18n) 解决方案。

**Rationale:**
*   **与 Next.js App Router 无缝集成**: 专为 App Router 设计，能充分利用其特性。
*   **强大的类型安全**: 核心优势，有助于在编译阶段捕获错误，提升代码质量和可维护性。
*   **良好的开发者体验 (DX)**: API 简洁，文档清晰，类型引导良好。
*   **支持 Server Components 和 Client Components**: 提供清晰的集成模式。
*   **性能考虑**: 支持按需加载区域设置。
*   **现代且前瞻的设计**: 符合 Next.js 的最新开发范式。
*   相较于 Next.js 内建方案，提供了更完善的翻译管理和类型安全。
*   相较于 `next-i18next`，在 App Router 中的集成更为原生和顺畅，避免了潜在的兼容性问题和适配成本。
*   相较于 `paraglide-js`，`next-international` 在 Next.js 社区中出现略早，API 更直接针对 Next.js，当前阶段更贴合项目需求。

**Implications/Details:**
*   开发团队需要熟悉 `next-international` 的 API 和配置方式。
*   翻译文件将按照 `next-international` 的推荐方式进行组织 (例如，在 `locales` 目录下按语言和客户端/服务器端区分)。
*   将利用其类型安全特性来确保翻译键的正确使用。
*   后续集成步骤将参考 [`memory-bank/activeContext.md`](memory-bank/activeContext.md:0) 中记录的简要集成概述。
*   **记录人**: 🏗️ 架构师 (在 NexusCore 指导下完成 TASK-I18N-RESEARCH-001)
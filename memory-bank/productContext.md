# 产品需求文档：阅后即焚网站

## 1. 项目概述

开发一款功能完善且用户体验优秀的阅后即焚网站。

## 2. 核心功能

*   用户输入文本信息。
*   可选设一次性访问密码。
*   自定义销毁条件：
    *   首次查看后立即销毁。
    *   特定时间后自动销毁（例如1小时、24小时），无论是否被查看。
*   系统生成独特、安全且不易猜测的分享链接。
*   链接接收者通过链接访问：
    *   若设有密码，需验证通过方可查看信息。
*   信息销毁机制：
    *   信息一旦被查看或达到预设销毁条件，即刻从服务器端被永久性、不可恢复地删除。
    *   分享链接同时失效。
    *   任何后续访问均提示信息不存在或已销毁。

## 3. 技术栈要求

*   **前端：**
    *   Next.js (最新稳定版，推荐App Router模式)
    *   JavaScript (ES6+)
    *   Tailwind CSS
    *   组件优先复用 heroUI 库，不足则基于 Tailwind CSS 自定义开发并确保风格统一。
*   **后端逻辑 (Next.js API Routes)：**
    *   信息创建
    *   加密处理 (若需)
    *   临时存储 (审慎选择方案，如 Vercel KV, Upstash Redis, 或评估内存缓存适用性)
    *   检索
    *   权限校验
    *   安全销毁 (核心目标：数据彻底安全清除)
*   **渲染模式：**
    *   信息创建页面和信息展示前的引导/密码输入页面：服务端渲染 (SSR)。

## 4. 设计与用户体验规范

*   **界面设计：** 现代化、极简主义美学。
*   **操作流程：** 直观易用。
*   **风格：** 统一采用圆角设计风格 (如 Tailwind CSS 的 `rounded-md` 或 `rounded-lg`)。
*   **字体：** 全局应用一套旨在实现类似苹果操作系统 (如 San Francisco 字体) 阅读体验的系统字体栈。
*   **图标：** 统一使用 heroicons 图标库 (推荐 SVG 格式，封装为可复用的 React 组件)。
*   **响应式设计：** 确保在桌面、平板和移动设备上均有良好的展示和操作体验。

## 5. 国际化要求

*   前端所有面向用户的文本内容均须支持国际化。
*   集成国际化库 (如 `next-i18next`) 进行多语言管理。
*   项目初期至少支持：
    *   中文 (`zh-CN`) (默认语言)
    *   英文 (`en-US`)

## 6. 代码质量与开发规范

*   **代码注释：** 项目中所有代码注释 (包括函数、复杂逻辑等) 必须使用中文撰写。
*   **代码规范与格式化：**
    *   配置并强制使用 ESLint 进行代码规范检查。
    *   使用 Prettier 进行代码格式化。
*   **安全：**
    *   确保全程 HTTPS 通信。
    *   对用户敏感输入 (如密码) 在传输和比较过程中进行适当处理。
    *   消息销毁机制必须可靠，防止数据被恢复。